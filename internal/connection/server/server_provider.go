/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_provider.go
 *
 * DESCRIPTION :    Server list provider interface definition
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

/*****************************************************************************
 * NAME: Provider
 *
 * DESCRIPTION:
 *     Interface for server list providers that can fetch server information
 *     from different sources (HTTP, file, etc.)
 *
 * METHODS:
 *     GetServerList() - Retrieves the list of available servers
 *     GetName()       - Returns the provider's name
 *     GetDescription() - Returns the provider's description
 *****************************************************************************/
type Provider interface {
	// GetServerList retrieves the list of available servers
	GetServerList() ([]Server, error)

	// GetServerListWithVersion retrieves the list of available servers with version information
	GetServerListWithVersion() (ServerListWithVersion, error)

	// GetName returns the provider's name
	GetName() string

	// GetDescription returns the provider's description
	GetDescription() string
}
