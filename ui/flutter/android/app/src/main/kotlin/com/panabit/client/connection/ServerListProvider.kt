/**
 * FILE: ServerListProvider.kt
 * AUTHOR: wei
 * DATE: 23/06/2025
 * VERSION: 1.0
 * DESCRIPTION: HTTP-based server list provider for fetching remote server configurations
 * HISTORY:
 *   23/06/2025 - Initial implementation
 */

package com.panabit.client.connection

import com.panabit.client.connection.models.ServerInfo
import com.panabit.client.connection.models.ServerStatus
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.decodeFromJsonElement
import java.net.HttpURLConnection
import java.net.URL
import java.io.BufferedReader
import java.io.InputStreamReader
import javax.net.ssl.HttpsURLConnection
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager
import java.security.cert.X509Certificate

/**
 * NAME: ServerListResponse
 *
 * DESCRIPTION:
 *     Data class representing the JSON response from server list API.
 *     Compatible with Go backend HTTP provider response format.
 *
 * PROPERTIES:
 *     version - Server list version string
 *     serverlist - List of server information objects
 */
@Serializable
data class ServerListResponse(
    val version: String,
    val serverlist: List<ServerListItem>
)

/**
 * NAME: ServerListItem
 *
 * DESCRIPTION:
 *     Data class representing individual server information in API response.
 *     Compatible with Go backend Server structure.
 *
 * PROPERTIES:
 *     id - Server unique identifier
 *     isauto - Whether this is an auto-select server
 *     name - Server display name (Chinese)
 *     name_en - Server display name (English)
 *     serverName - Server hostname or IP address
 *     serverPort - Server port number
 */
@Serializable
data class ServerListItem(
    val id: Int,
    val isauto: Boolean,
    val name: String,
    val name_en: String,
    val serverName: String,
    val serverPort: Int
)

/**
 * NAME: ServerListProvider
 *
 * DESCRIPTION:
 *     HTTP-based server list provider that fetches server information from remote endpoints.
 *     Implements retry logic, timeout handling, and SSL configuration.
 *     Compatible with Go backend HTTPProvider functionality.
 *
 * FEATURES:
 *     - HTTP/HTTPS support with configurable SSL verification
 *     - Request timeout and retry mechanism
 *     - JSON response parsing compatible with Go backend
 *     - Error handling and logging
 *     - Thread-safe operations using coroutines
 */
class ServerListProvider(
    private val baseUrl: String,
    private val timeout: Long = 25_000L, // 25 seconds (aligned with iOS)
    private val skipSslVerification: Boolean = false
) {
    companion object {
        private const val TAG = "ServerListProvider"
        private const val DEFAULT_PORT = 8000
        private const val MAX_RETRIES = 3
        private const val RETRY_DELAY = 2_000L // 2 seconds
    }

    private val json = Json { 
        ignoreUnknownKeys = true
        isLenient = true
    }

    init {
        logInfo("ServerListProvider initialized", mapOf(
            "base_url" to baseUrl,
            "timeout" to timeout,
            "skip_ssl_verification" to skipSslVerification
        ))
        
        if (skipSslVerification) {
            configureTrustAllSSL()
        }
    }

    /**
     * NAME: fetchServerList
     *
     * DESCRIPTION:
     *     Fetches server list from remote HTTP endpoint.
     *     Implements retry logic and timeout handling.
     *
     * RETURNS:
     *     List<ServerInfo> - List of server information objects
     *
     * THROWS:
     *     Exception - If all retry attempts fail or response is invalid
     */
    suspend fun fetchServerList(): List<ServerInfo> = withContext(Dispatchers.IO) {
        logInfo("Fetching server list from remote endpoint")
        
        var lastException: Exception? = null
        
        repeat(MAX_RETRIES) { attempt ->
            try {
                val serverList = performHttpRequest()
                logInfo("Successfully fetched server list", mapOf(
                    "server_count" to serverList.size,
                    "attempt" to (attempt + 1)
                ))
                return@withContext serverList
            } catch (e: Exception) {
                lastException = e
                logWarn("Server list fetch attempt ${attempt + 1} failed: ${e.message}")
                
                if (attempt < MAX_RETRIES - 1) {
                    logDebug("Retrying in ${RETRY_DELAY}ms...")
                    kotlinx.coroutines.delay(RETRY_DELAY)
                }
            }
        }
        
        logError("All server list fetch attempts failed", lastException)
        throw lastException ?: Exception("Failed to fetch server list after $MAX_RETRIES attempts")
    }

    /**
     * NAME: performHttpRequest
     *
     * DESCRIPTION:
     *     Performs the actual HTTP request to fetch server list.
     *     Handles both HTTP and HTTPS protocols.
     *
     * RETURNS:
     *     List<ServerInfo> - Parsed server list
     *
     * THROWS:
     *     Exception - If request fails or response parsing fails
     */
    private suspend fun performHttpRequest(): List<ServerInfo> = withTimeout(timeout) {
        val url = URL(baseUrl)  // Use baseUrl directly, don't append /servers
        logDebug("Making HTTP request to: $url")
        
        val connection = url.openConnection() as HttpURLConnection
        
        try {
            // Configure connection
            connection.requestMethod = "GET"
            connection.connectTimeout = (timeout / 2).toInt() // Half timeout for connect
            connection.readTimeout = (timeout / 2).toInt()    // Half timeout for read
            connection.setRequestProperty("Accept", "application/json")
            connection.setRequestProperty("User-Agent", "ITforce-WAN-Android/1.0")
            
            // Check response code
            val responseCode = connection.responseCode
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw Exception("HTTP request failed with code: $responseCode")
            }
            
            // Read response
            val response = BufferedReader(InputStreamReader(connection.inputStream)).use { reader ->
                reader.readText()
            }
            
            logDebug("Received response", mapOf("response_length" to response.length))
            
            // Parse JSON response
            return@withTimeout parseServerListResponse(response)
            
        } finally {
            connection.disconnect()
        }
    }

    /**
     * NAME: parseServerListResponse
     *
     * DESCRIPTION:
     *     Parses JSON response and converts to ServerInfo objects.
     *     Compatible with Go backend response format.
     *     Returns both server list and version information.
     *
     * PARAMETERS:
     *     jsonResponse - JSON response string
     *
     * RETURNS:
     *     ServerListResponse - Parsed server list with version information
     *
     * THROWS:
     *     Exception - If JSON parsing fails or response is invalid
     */
    private fun parseServerListResponse(jsonResponse: String): ServerListResponse {
        try {
            // Parse JSON as generic map first to support multiple formats
            val jsonMap = json.decodeFromString<Map<String, kotlinx.serialization.json.JsonElement>>(jsonResponse)

            // Extract version information
            val version = when {
                jsonMap.containsKey("version") -> {
                    json.decodeFromJsonElement<String>(jsonMap["version"]!!)
                }
                else -> {
                    // Generate version based on content hash if not provided
                    jsonResponse.hashCode().toString()
                }
            }

            // Try to extract server list array from different formats
            val serverListArray = when {
                // Format 1: {"version": "1.0", "serverlist": [...]}
                jsonMap.containsKey("serverlist") -> {
                    logDebug("Found serverlist format")
                    json.decodeFromJsonElement<List<ServerListItem>>(jsonMap["serverlist"]!!)
                }
                // Format 2: {"success": true, "data": [...]}
                jsonMap.containsKey("data") -> {
                    logDebug("Found data format")
                    json.decodeFromJsonElement<List<ServerListItem>>(jsonMap["data"]!!)
                }
                else -> {
                    val availableKeys = jsonMap.keys.joinToString(", ")
                    throw Exception("No serverlist or data array found. Available keys: $availableKeys")
                }
            }

            if (serverListArray.isEmpty()) {
                throw Exception("Empty server list received")
            }

            logInfo("Parsed server list response", mapOf(
                "format" to if (jsonMap.containsKey("serverlist")) "serverlist" else "data",
                "version" to version,
                "server_count" to serverListArray.size
            ))

            val servers = serverListArray.map { item ->
                val serverPort = if (item.serverPort > 0) item.serverPort else DEFAULT_PORT

                ServerInfo(
                    id = item.id.toString(),
                    name = item.name,
                    nameEn = item.name_en,
                    serverName = item.serverName,
                    serverPort = serverPort,
                    isAuto = item.isauto,
                    ping = 0, // Will be updated by ping operations
                    status = ServerStatus.Unknown,
                    lastCheck = System.currentTimeMillis()
                )
            }

            return ServerListResponse(servers, version)

        } catch (e: Exception) {
            logError("Failed to parse server list response: ${e.message}", e)
            throw Exception("Failed to parse server list response: ${e.message}")
        }
    }

    /**
     * NAME: configureTrustAllSSL
     *
     * DESCRIPTION:
     *     Configures SSL context to trust all certificates.
     *     Used for development or when SSL verification needs to be bypassed.
     */
    private fun configureTrustAllSSL() {
        try {
            val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
                override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {}
                override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
            })

            val sslContext = SSLContext.getInstance("SSL")
            sslContext.init(null, trustAllCerts, java.security.SecureRandom())
            HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.socketFactory)
            HttpsURLConnection.setDefaultHostnameVerifier { _, _ -> true }
            
            logWarn("SSL certificate verification disabled")
        } catch (e: Exception) {
            logError("Failed to configure SSL trust all: ${e.message}", e)
        }
    }
}
