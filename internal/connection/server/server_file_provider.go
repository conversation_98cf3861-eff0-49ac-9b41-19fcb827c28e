/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_file_provider.go
 *
 * DESCRIPTION :    File-based server list provider implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"

	"mobile/internal/common/logger"
)

/*****************************************************************************
 * NAME: FileProvider
 *
 * DESCRIPTION:
 *     File-based server list provider that reads server information
 *     from local JSON files
 *
 * FIELDS:
 *     filePath - Server list file path
 *     logger   - Logger instance
 *****************************************************************************/
type FileProvider struct {
	filePath string        // Server list file path
	logger   logger.Logger // Logger instance
}

/*****************************************************************************
 * NAME: NewFileProvider
 *
 * DESCRIPTION:
 *     Creates a new file-based server list provider instance
 *
 * PARAMETERS:
 *     filePath - Path to the server list file
 *     log      - Logger instance
 *
 * RETURNS:
 *     *FileProvider - New file provider instance
 *****************************************************************************/
func NewFileProvider(filePath string, log logger.Logger) *FileProvider {
	return &FileProvider{
		filePath: filePath,
		logger:   log.WithModule("file-provider"),
	}
}

/*****************************************************************************
 * NAME: GetServerList
 *
 * DESCRIPTION:
 *     Retrieves server list from file, trying multiple possible paths
 *     including executable directory and current working directory
 *
 * RETURNS:
 *     []Server - List of servers from file
 *     error    - Error if file cannot be read or parsed
 *****************************************************************************/
func (p *FileProvider) GetServerList() ([]Server, error) {
	p.logger.Info("Loading server list from file", logger.String("path", p.filePath))

	// Try multiple paths to find the file
	paths := []string{
		p.filePath, // Original path
	}

	// Get executable file path
	exePath, err := os.Executable()
	if err == nil {
		exeDir := filepath.Dir(exePath)
		p.logger.Debug("Executable directory", logger.String("exe_dir", exeDir))
		// Add path under executable directory
		paths = append(paths, filepath.Join(exeDir, p.filePath))
		// Add path under parent directory of executable
		paths = append(paths, filepath.Join(filepath.Dir(exeDir), p.filePath))
	} else {
		p.logger.Warn("Failed to get executable path", logger.ErrorField(err))
	}

	// Get current working directory
	cwd, err := os.Getwd()
	if err == nil {
		p.logger.Debug("Current working directory", logger.String("cwd", cwd))
		// Add path under current working directory
		paths = append(paths, filepath.Join(cwd, p.filePath))
	} else {
		p.logger.Warn("Failed to get current working directory", logger.ErrorField(err))
	}

	// Print all paths to try
	for i, path := range paths {
		p.logger.Debug("Will try server list file path",
			logger.Int("index", i),
			logger.String("path", path))
	}

	// Try all possible paths
	var lastErr error
	for _, path := range paths {
		p.logger.Debug("Trying server list file path", logger.String("path", path))

		// Check if file exists
		fileInfo, err := os.Stat(path)
		if os.IsNotExist(err) {
			p.logger.Debug("File does not exist", logger.String("path", path))
			lastErr = err
			continue
		} else if err != nil {
			p.logger.Debug("Error checking file", logger.String("path", path), logger.ErrorField(err))
			lastErr = err
			continue
		}

		p.logger.Debug("File exists",
			logger.String("path", path),
			logger.Int64("size", fileInfo.Size()),
			logger.String("mode", fileInfo.Mode().String()))

		// Read file content
		data, err := ioutil.ReadFile(path)
		if err != nil {
			p.logger.Warn("Failed to read file", logger.String("path", path), logger.ErrorField(err))
			lastErr = fmt.Errorf("failed to read server list file %s: %w", path, err)
			continue
		}

		p.logger.Info("Successfully found and read server list file",
			logger.String("path", path),
			logger.Int("content_length", len(data)))

		// Parse JSON
		// First try to parse standard format
		var serverList ServerList
		if err := json.Unmarshal(data, &serverList); err != nil {
			p.logger.Warn("Failed to parse server list as standard format",
				logger.String("path", path),
				logger.ErrorField(err))

			// Print part of file content for debugging
			preview := string(data)
			if len(preview) > 200 {
				preview = preview[:200] + "..."
			}
			p.logger.Debug("File content preview", logger.String("preview", preview))

			// If standard format parsing fails, try alternative format (isauto as integer)
			p.logger.Debug("Trying alternative format (isauto as integer)")
			var altServerList struct {
				Version    string `json:"version"`
				ServerList []struct {
					ID         int    `json:"id"`
					IsAuto     int    `json:"isauto"`
					Name       string `json:"name"`
					NameEn     string `json:"name_en"`
					ServerName string `json:"serverName"`
					ServerPort int    `json:"serverPort"`
				} `json:"serverlist"`
			}

			if err2 := json.Unmarshal(data, &altServerList); err2 != nil {
				p.logger.Warn("Failed to parse server list as alternative format",
					logger.String("path", path),
					logger.ErrorField(err2))

				// Try parsing other possible formats
				p.logger.Debug("Trying other possible formats")

				// Try parsing simple format
				var simpleList []Server
				if err3 := json.Unmarshal(data, &simpleList); err3 == nil && len(simpleList) > 0 {
					p.logger.Info("Parsed server list as simple array format",
						logger.String("path", path),
						logger.Int("count", len(simpleList)))
					return simpleList, nil
				}

				// Try parsing other format
				var altFormat struct {
					Servers []Server `json:"servers"`
				}
				if err4 := json.Unmarshal(data, &altFormat); err4 == nil && len(altFormat.Servers) > 0 {
					p.logger.Info("Parsed server list as alternative format with 'servers' field",
						logger.String("path", path),
						logger.Int("count", len(altFormat.Servers)))
					return altFormat.Servers, nil
				}

				lastErr = fmt.Errorf("failed to parse server list file %s: %w", path, err)
				continue
			}

			// Convert to standard format
			p.logger.Info("Parsed server list as alternative format with integer isauto",
				logger.String("path", path),
				logger.String("version", altServerList.Version),
				logger.Int("count", len(altServerList.ServerList)))

			serverList.Version = altServerList.Version
			serverList.ServerList = make([]Server, len(altServerList.ServerList))
			for i, s := range altServerList.ServerList {
				serverList.ServerList[i] = Server{
					ID:         fmt.Sprintf("%d", s.ID),
					IsAuto:     s.IsAuto != 0, // Convert number to boolean
					Name:       s.Name,
					NameEn:     s.NameEn,
					ServerName: s.ServerName,
					ServerPort: s.ServerPort,
					Status:     "unknown",
				}
			}
		} else {
			// Check if server list is empty
			if len(serverList.ServerList) == 0 {
				p.logger.Warn("Server list is empty", logger.String("path", path))
				lastErr = fmt.Errorf("server list is empty in file %s", path)
				continue
			}
		}

		p.logger.Info("Loaded server list from file",
			logger.String("path", path),
			logger.String("version", serverList.Version),
			logger.Int("count", len(serverList.ServerList)))

		// Print first server information as example
		if len(serverList.ServerList) > 0 {
			firstServer := serverList.ServerList[0]
			p.logger.Debug("First server example",
				logger.String("id", firstServer.ID),
				logger.String("name", firstServer.Name),
				logger.String("server_name", firstServer.ServerName),
				logger.Int("server_port", firstServer.ServerPort),
				logger.Bool("is_auto", firstServer.IsAuto))
		}

		return serverList.ServerList, nil
	}

	// If all paths failed, return the last error
	if lastErr != nil {
		return nil, fmt.Errorf("failed to load server list from any path: %w", lastErr)
	}

	return nil, fmt.Errorf("no server list file found")
}

/*****************************************************************************
 * NAME: GetServerListWithVersion
 *
 * DESCRIPTION:
 *     Retrieves server list with version information from file.
 *     Implements VersionedProvider interface for optimized updates.
 *
 * RETURNS:
 *     ServerListWithVersion - Server list with version information
 *     error                 - Error if file cannot be read or parsed
 *****************************************************************************/
func (p *FileProvider) GetServerListWithVersion() (ServerListWithVersion, error) {
	p.logger.Info("Loading server list with version from file", logger.String("path", p.filePath))

	// Try multiple paths to find the file
	paths := []string{
		p.filePath, // Original path
	}

	// Add executable directory path
	if execPath, err := os.Executable(); err == nil {
		execDir := filepath.Dir(execPath)
		paths = append(paths, filepath.Join(execDir, filepath.Base(p.filePath)))
	}

	// Add current working directory path
	if cwd, err := os.Getwd(); err == nil {
		paths = append(paths, filepath.Join(cwd, filepath.Base(p.filePath)))
	}

	var lastErr error
	for _, path := range paths {
		p.logger.Debug("Trying to read server list file", logger.String("path", path))

		// Check if file exists
		if _, err := os.Stat(path); os.IsNotExist(err) {
			p.logger.Debug("File does not exist", logger.String("path", path))
			lastErr = fmt.Errorf("file does not exist: %s", path)
			continue
		}

		// Read file content
		data, err := ioutil.ReadFile(path)
		if err != nil {
			p.logger.Warn("Failed to read file", logger.String("path", path), logger.ErrorField(err))
			lastErr = fmt.Errorf("failed to read server list file %s: %w", path, err)
			continue
		}

		p.logger.Info("Successfully found and read server list file",
			logger.String("path", path),
			logger.Int("content_length", len(data)))

		// Parse JSON with version
		serverListWithVersion, err := p.parseServerListWithVersion(data, path)
		if err != nil {
			lastErr = err
			continue
		}

		return serverListWithVersion, nil
	}

	// If all paths failed, return the last error
	if lastErr != nil {
		return ServerListWithVersion{}, fmt.Errorf("failed to load server list from any path: %w", lastErr)
	}

	return ServerListWithVersion{}, fmt.Errorf("no valid server list file found")
}

/*****************************************************************************
 * NAME: parseServerListWithVersion
 *
 * DESCRIPTION:
 *     Parses JSON file content and extracts both server list and version information
 *
 * PARAMETERS:
 *     data - JSON file content
 *     path - File path for logging
 *
 * RETURNS:
 *     ServerListWithVersion - Parsed server list with version
 *     error                 - Error if parsing fails
 *****************************************************************************/
func (p *FileProvider) parseServerListWithVersion(data []byte, path string) (ServerListWithVersion, error) {
	// Parse JSON
	// First try to parse standard format
	var serverList ServerList
	if err := json.Unmarshal(data, &serverList); err != nil {
		p.logger.Warn("Failed to parse server list as standard format",
			logger.String("path", path),
			logger.ErrorField(err))

		// Print part of file content for debugging
		preview := string(data)
		if len(preview) > 200 {
			preview = preview[:200] + "..."
		}
		p.logger.Debug("File content preview", logger.String("preview", preview))

		// If standard format parsing fails, try alternative format (isauto as integer)
		p.logger.Debug("Trying alternative format (isauto as integer)")
		var altServerList struct {
			Version    string `json:"version"`
			ServerList []struct {
				ID         int    `json:"id"`
				IsAuto     int    `json:"isauto"`
				Name       string `json:"name"`
				NameEn     string `json:"name_en"`
				ServerName string `json:"serverName"`
				ServerPort int    `json:"serverPort"`
			} `json:"serverlist"`
		}

		if err2 := json.Unmarshal(data, &altServerList); err2 != nil {
			p.logger.Warn("Failed to parse server list as alternative format",
				logger.String("path", path),
				logger.ErrorField(err2))

			// Try parsing other possible formats
			p.logger.Debug("Trying other possible formats")

			// Try parsing simple format
			var simpleList []Server
			if err3 := json.Unmarshal(data, &simpleList); err3 == nil && len(simpleList) > 0 {
				p.logger.Info("Parsed server list as simple array format",
					logger.String("path", path),
					logger.Int("count", len(simpleList)))
				return ServerListWithVersion{
					Servers: simpleList,
					Version: "unknown", // No version in simple format
				}, nil
			}

			// Try parsing other format
			var altFormat struct {
				Servers []Server `json:"servers"`
			}
			if err4 := json.Unmarshal(data, &altFormat); err4 == nil && len(altFormat.Servers) > 0 {
				p.logger.Info("Parsed server list as alternative format with 'servers' field",
					logger.String("path", path),
					logger.Int("count", len(altFormat.Servers)))
				return ServerListWithVersion{
					Servers: altFormat.Servers,
					Version: "unknown", // No version in this format
				}, nil
			}

			return ServerListWithVersion{}, fmt.Errorf("failed to parse server list file %s: %w", path, err)
		}

		// Convert to standard format
		p.logger.Info("Parsed server list as alternative format with integer isauto",
			logger.String("path", path),
			logger.String("version", altServerList.Version),
			logger.Int("count", len(altServerList.ServerList)))

		serverList.Version = altServerList.Version
		serverList.ServerList = make([]Server, len(altServerList.ServerList))
		for i, s := range altServerList.ServerList {
			serverList.ServerList[i] = Server{
				ID:         fmt.Sprintf("%d", s.ID),
				IsAuto:     s.IsAuto != 0, // Convert number to boolean
				Name:       s.Name,
				NameEn:     s.NameEn,
				ServerName: s.ServerName,
				ServerPort: s.ServerPort,
				Status:     "unknown",
			}
		}
	} else {
		// Check if server list is empty
		if len(serverList.ServerList) == 0 {
			p.logger.Warn("Server list is empty", logger.String("path", path))
			return ServerListWithVersion{}, fmt.Errorf("server list is empty in file %s", path)
		}
	}

	p.logger.Info("Loaded server list from file with version",
		logger.String("path", path),
		logger.String("version", serverList.Version),
		logger.Int("count", len(serverList.ServerList)))

	// Print first server information as example
	if len(serverList.ServerList) > 0 {
		firstServer := serverList.ServerList[0]
		p.logger.Debug("First server example",
			logger.String("id", firstServer.ID),
			logger.String("name", firstServer.Name),
			logger.String("server_name", firstServer.ServerName),
			logger.Int("server_port", firstServer.ServerPort),
			logger.Bool("is_auto", firstServer.IsAuto))
	}

	return ServerListWithVersion{
		Servers: serverList.ServerList,
		Version: serverList.Version,
	}, nil
}

/*****************************************************************************
 * NAME: GetName
 *
 * DESCRIPTION:
 *     Returns the provider's name
 *
 * RETURNS:
 *     string - Provider name
 *****************************************************************************/
func (p *FileProvider) GetName() string {
	return "File Provider"
}

/*****************************************************************************
 * NAME: GetDescription
 *
 * DESCRIPTION:
 *     Returns the provider's description including the file name
 *
 * RETURNS:
 *     string - Provider description
 *****************************************************************************/
func (p *FileProvider) GetDescription() string {
	return fmt.Sprintf("File server list provider (%s)", filepath.Base(p.filePath))
}
