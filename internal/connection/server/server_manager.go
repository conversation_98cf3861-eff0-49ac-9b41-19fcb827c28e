/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_manager.go
 *
 * DESCRIPTION :    Server manager implementation for VPN server management
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

import (
	"fmt"
	"net"
	"sort"
	"sync"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/platform/network"
)

// Use common network interface type
type PhysicalInterfaceInfo = network.PhysicalInterfaceInfo

/*****************************************************************************
 * NAME: Manager
 *
 * DESCRIPTION:
 *     Server manager for VPN server management, handles server list updates,
 *     ping operations, route management, and network monitoring
 *
 * FIELDS:
 *     providers              - Server list providers
 *     servers                - Current server list
 *     defaultServerID        - Default server ID
 *     mutex                  - Read-write lock for server list access
 *     updateTicker           - Timer for server list updates
 *     pingTicker             - Timer for ping operations
 *     networkTicker          - Timer for network monitoring
 *     stopChan               - Stop channel for background tasks
 *     updateCallbacks        - Callbacks for server list updates
 *     statusCallbacks        - Callbacks for status changes
 *     networkChangeCallbacks - Callbacks for network changes
 *     logger                 - Logger instance
 *     config                 - Manager configuration
 *     pingInProgress         - Flag indicating ping operation in progress
 *     pingMutex              - Mutex for ping operation synchronization
 *     serverRoutes           - Server route mapping (server ID -> IP list)
 *     routeMutex             - Mutex for route operations
 *     physicalGateway        - Physical interface gateway
 *     physicalIfaceInfo      - Physical interface information
 *     physicalNetInterface   - NetworkInterface abstraction for physical interface
 *     retryTicker            - Timer for server list retry mechanism
 *     hasServerList          - Flag indicating if server list has been obtained
 *****************************************************************************/
type Manager struct {
	providers              []Provider               // Server list providers
	servers                []Server                 // Current server list
	defaultServerID        string                   // Default server ID
	mutex                  sync.RWMutex             // Read-write lock for server list access
	updateTicker           *time.Ticker             // Timer for server list updates
	pingTicker             *time.Ticker             // Timer for ping operations
	networkTicker          *time.Ticker             // Timer for network monitoring
	stopChan               chan struct{}            // Stop channel for background tasks
	updateCallbacks        []func([]Server)         // Callbacks for server list updates
	statusCallbacks        []func(string, string)   // Callbacks for status changes
	networkChangeCallbacks []func()                 // Callbacks for network changes
	logger                 logger.Logger            // Logger instance
	config                 Config                   // Manager configuration
	pingInProgress         bool                     // Flag indicating ping operation in progress
	pingMutex              sync.Mutex               // Mutex for ping operation synchronization
	serverRoutes           map[string][]net.IP      // Server route mapping (server ID -> IP list)
	routeMutex             sync.RWMutex             // Mutex for route operations
	physicalGateway        net.IP                   // Physical interface gateway
	physicalIfaceInfo      *PhysicalInterfaceInfo   // Physical interface information
	physicalNetInterface   network.NetworkInterface // NetworkInterface abstraction for physical interface
	retryTicker            *time.Ticker             // Timer for server list retry mechanism
	hasServerList          bool                     // Flag indicating if server list has been obtained
	lastServerListVersion  string                   // Track server list version to avoid unnecessary UI updates

	// IP Cache Management
	serverIPCache map[string]string // Simple IP cache for DNS resolution (hostname -> IP)
	ipCacheMutex  sync.RWMutex      // Mutex for IP cache operations
}

/*****************************************************************************
 * NAME: NewManager
 *
 * DESCRIPTION:
 *     Creates a new server manager instance with the provided configuration
 *     and logger
 *
 * PARAMETERS:
 *     config - Manager configuration
 *     log    - Logger instance
 *
 * RETURNS:
 *     *Manager - New manager instance
 *****************************************************************************/
func NewManager(config Config, log logger.Logger) *Manager {
	return &Manager{
		providers:              []Provider{},
		servers:                []Server{},
		defaultServerID:        "",
		updateCallbacks:        []func([]Server){},
		statusCallbacks:        []func(string, string){},
		networkChangeCallbacks: []func(){},

		stopChan:      make(chan struct{}),
		logger:        log.WithModule("server-manager"),
		config:        config,
		serverRoutes:  make(map[string][]net.IP),
		serverIPCache: make(map[string]string), // Initialize IP cache
	}
}

/*****************************************************************************
 * NAME: AddProvider
 *
 * DESCRIPTION:
 *     Adds a server list provider to the manager
 *
 * PARAMETERS:
 *     provider - Server list provider to add
 *****************************************************************************/
func (m *Manager) AddProvider(provider Provider) {
	m.providers = append(m.providers, provider)
	m.logger.Info("Added server list provider",
		logger.String("provider", provider.GetName()),
		logger.String("description", provider.GetDescription()))
}

/*****************************************************************************
 * NAME: Initialize
 *
 * DESCRIPTION:
 *     Initializes the server manager by setting up providers based on
 *     configuration
 *
 * RETURNS:
 *     error - Error if initialization fails
 *****************************************************************************/
func (m *Manager) Initialize() error {
	m.logger.Info("Initializing server manager")

	// Log configuration information
	m.logger.Info("Server manager configuration",
		logger.String("server_list_url", m.config.ServerListURL),
		logger.String("server_list_file", m.config.ServerListFile),
		logger.Duration("update_interval", m.config.UpdateInterval),
		logger.Duration("ping_interval", m.config.PingInterval))

	// RESTORED: Automatically add providers based on config.yaml
	// This restores the backend to use static configuration instead of dynamic user input
	m.logger.Info("Adding providers based on static configuration")

	// Add file provider if configured
	if m.config.ServerListFile != "" {
		m.logger.Info("Adding file provider", logger.String("file", m.config.ServerListFile))
		m.AddProvider(NewFileProvider(m.config.ServerListFile, m.logger))
	}

	// Add HTTP provider if configured
	if m.config.ServerListURL != "" {
		m.logger.Info("Adding HTTP provider",
			logger.String("url", m.config.ServerListURL),
			logger.Bool("tls_skip_verify", m.config.TLSSkipVerify))
		m.AddProvider(NewHTTPProvider(m.config.ServerListURL, 10*time.Second, m.config.TLSSkipVerify, m.logger))
	}

	return nil
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the server manager by initializing timers, background tasks,
 *     and updating server list
 *
 * RETURNS:
 *     error - Error if startup fails
 *****************************************************************************/
func (m *Manager) Start() error {
	m.logger.Info("Starting server manager")

	// First get physical interface information
	err := m.initializePhysicalInterface()
	if err != nil {
		m.logger.Error("Failed to initialize physical interface info", logger.ErrorField(err))
		// Don't interrupt startup process, but log the error
	}

	// Initialize update timer
	m.updateTicker = time.NewTicker(m.config.UpdateInterval)

	// Initialize ping timer
	m.pingTicker = time.NewTicker(m.config.PingInterval)

	// Initialize network monitoring timer
	m.networkTicker = time.NewTicker(m.config.NetworkMonitorInterval)

	// Initialize retry timer (but don't start it, set to a very long interval)
	m.retryTicker = time.NewTicker(24 * time.Hour) // 24 hours, won't actually trigger
	m.retryTicker.Stop()                           // Stop immediately

	// Start background tasks
	go func() {
		defer func() {
			if r := recover(); r != nil {
				m.logger.Error("PANIC in background task",
					logger.Any("panic", r))
				errors.LogPanic(m.logger, r)
				// Restart background task
				go m.backgroundTask()
			}
		}()
		m.backgroundTask()
	}()

	// Only try to update server list if providers are available
	// If no providers, wait for frontend to set URL
	if len(m.providers) > 0 {
		// Use longer timeout to wait for ping completion, ensure accurate latency info during initialization
		pingTimeout := time.Duration(len(m.servers)+5) * m.config.PingTimeout
		if pingTimeout < 3*time.Second {
			pingTimeout = 3 * time.Second
		}

		err := m.UpdateServerListAndWaitPing(pingTimeout)
		if err != nil {
			m.logger.Error("Failed to update server list during startup, will start retry mechanism",
				logger.ErrorField(err))
			// Start retry mechanism
			m.startServerListRetry()
		} else {
			m.hasServerList = true
		}
	} else {
		m.logger.Info("No providers configured yet, waiting for frontend to set server list URL")
	}

	return nil
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the server manager by stopping timers, cleaning up routes,
 *     and closing background tasks
 *
 * RETURNS:
 *     error - Error if stop operation fails
 *****************************************************************************/
func (m *Manager) Stop() error {
	m.logger.Info("Stopping server manager")

	// Stop timers
	if m.updateTicker != nil {
		m.updateTicker.Stop()
	}
	if m.pingTicker != nil {
		m.pingTicker.Stop()
	}
	if m.networkTicker != nil {
		m.networkTicker.Stop()
	}
	if m.retryTicker != nil {
		m.retryTicker.Stop()
	}

	// Clean up server routes
	m.removeServerRoutes()

	// Send stop signal
	close(m.stopChan)

	return nil
}

/*****************************************************************************
 * NAME: GetServers
 *
 * DESCRIPTION:
 *     Gets a copy of all servers, ensuring thread safety
 *
 * RETURNS:
 *     []Server - Copy of server list
 *****************************************************************************/
func (m *Manager) GetServers() []Server {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	servers := make([]Server, len(m.servers))
	copy(servers, m.servers)

	return servers
}

/*****************************************************************************
 * NAME: GetServer
 *
 * DESCRIPTION:
 *     Gets server information by ID
 *
 * PARAMETERS:
 *     id - Server ID
 *
 * RETURNS:
 *     *Server - Copy of server information
 *     error   - Error if server does not exist
 *****************************************************************************/
func (m *Manager) GetServer(id string) (*Server, error) {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	for _, server := range m.servers {
		if server.ID == id {
			serverCopy := server
			return &serverCopy, nil
		}
	}

	return nil, fmt.Errorf("server not found: %s", id)
}

/*****************************************************************************
 * NAME: GetBestServer
 *
 * DESCRIPTION:
 *     Gets the best server (online server with lowest latency)
 *
 * RETURNS:
 *     *Server - Copy of best server
 *     error   - Error if no available servers
 *****************************************************************************/
func (m *Manager) GetBestServer() (*Server, error) {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	if len(m.servers) == 0 {
		return nil, fmt.Errorf("no servers available")
	}

	// Find server with lowest latency
	var bestServer *Server
	bestPing := 999999

	for _, server := range m.servers {
		// Skip offline servers
		if server.Status == "offline" {
			continue
		}

		// Skip servers with 0 ping (not measured or measurement failed)
		if server.Ping > 0 && server.Ping < bestPing {
			bestPing = server.Ping
			serverCopy := server
			bestServer = &serverCopy
		}
	}

	// If no valid server found, return the first server
	if bestServer == nil {
		serverCopy := m.servers[0]
		bestServer = &serverCopy
	}

	return bestServer, nil
}

/*****************************************************************************
 * NAME: GetDefaultServer
 *
 * DESCRIPTION:
 *     Gets default server information
 *
 * RETURNS:
 *     *Server - Copy of default server
 *     error   - Error if no default server set or server does not exist
 *****************************************************************************/
func (m *Manager) GetDefaultServer() (*Server, error) {
	//m.mutex.RLock()
	//defer m.mutex.RUnlock()

	if m.defaultServerID == "" {
		return nil, fmt.Errorf("no default server set")
	}

	for _, server := range m.servers {
		if server.ID == m.defaultServerID {
			serverCopy := server
			return &serverCopy, nil
		}
	}

	return nil, fmt.Errorf("default server not found: %s", m.defaultServerID)
}

/*****************************************************************************
 * NAME: UpdateServerListAndWaitPing
 *
 * DESCRIPTION:
 *     Updates server list and waits for ping completion (used for initialization)
 *
 * PARAMETERS:
 *     pingTimeout - Timeout duration for ping completion
 *
 * RETURNS:
 *     error - Error if update or ping fails
 *****************************************************************************/
func (m *Manager) UpdateServerListAndWaitPing(pingTimeout time.Duration) error {
	m.logger.Info("Updating server list and waiting for ping completion", logger.Duration("ping_timeout", pingTimeout))

	// Update server list
	if err := m.UpdateServerList(); err != nil {
		return fmt.Errorf("failed to update server list: %w", err)
	}

	// Wait for ping completion
	if err := m.WaitForPingCompletion(pingTimeout); err != nil {
		m.logger.Warn("Ping completion timeout, but continuing", logger.ErrorField(err))
		// Don't return error, allow continuing
	}

	m.logger.Info("Server list update and ping completion finished")
	return nil
}

/*****************************************************************************
 * NAME: UpdateServerList
 *
 * DESCRIPTION:
 *     Updates server list from all configured providers
 *
 * RETURNS:
 *     error - Error if update fails
 *****************************************************************************/
func (m *Manager) UpdateServerList() error {
	m.logger.Info("Updating server list")

	// Track if at least one provider was tried
	atLeastOneProviderTried := false

	// Iterate through all providers
	for _, provider := range m.providers {
		atLeastOneProviderTried = true
		servers, err := provider.GetServerList()
		if err != nil {
			m.logger.Error("Failed to get server list from provider",
				logger.String("provider", provider.GetName()),
				logger.ErrorField(err))
			continue
		}

		if len(servers) > 0 {
			// Get existing server information (using read lock)
			//m.mutex.RLock()
			existingServers := make(map[string]Server)
			for _, server := range m.servers {
				existingServers[server.ID] = server
			}
			//m.mutex.RUnlock()

			// Preserve existing server status and latency information
			for i := range servers {
				if existingServer, ok := existingServers[servers[i].ID]; ok {
					servers[i].Ping = existingServer.Ping
					servers[i].Status = existingServer.Status
					servers[i].LastCheck = existingServer.LastCheck
				}
			}

			// Update server list (using write lock)
			//m.mutex.Lock()
			m.servers = servers
			//m.mutex.Unlock()

			// Add server routes to physical interface
			m.addServerRoutes(servers)

			// Notify callbacks
			m.notifyUpdateCallbacks()

			// Mark server list obtained, stop retry
			if !m.hasServerList {
				m.hasServerList = true
				m.stopServerListRetry()
				m.logger.Info("Server list obtained successfully, stopping retry mechanism")
			}

			// Pre-resolve all server IPs in background to avoid DNS failures during connection/ping
			m.logger.Info("Starting batch IP resolution for server list",
				logger.Int("server_count", len(servers)))

			go func() {
				defer func() {
					if r := recover(); r != nil {
						m.logger.Error("PANIC in IP resolution goroutine",
							logger.Any("panic", r))
						errors.LogPanic(m.logger, r)
					}
				}()

				successCount := 0
				for _, server := range servers {
					if resolvedIP, err := m.resolveHostnameToIP(server.ServerName); err == nil && resolvedIP != "" {
						m.ipCacheMutex.Lock()
						m.serverIPCache[server.ServerName] = resolvedIP
						m.ipCacheMutex.Unlock()
						successCount++
					} else {
						m.logger.Warn("Failed to resolve server IP during batch resolution",
							logger.String("hostname", server.ServerName),
							logger.String("server_id", server.ID),
							logger.String("error", err.Error()))
					}
				}

				m.logger.Info("Batch IP resolution completed",
					logger.Int("total_servers", len(servers)),
					logger.Int("successful_resolves", successCount),
					logger.Int("failed_resolves", len(servers)-successCount))
			}()

			// Measure server latency (start outside lock to avoid deadlock)
			go func() {
				defer func() {
					if r := recover(); r != nil {
						m.logger.Error("PANIC in post-update ping goroutine",
							logger.Any("panic", r))
						errors.LogPanic(m.logger, r)
					}
				}()

				// Add brief delay to ensure server list update completion
				time.Sleep(100 * time.Millisecond)
				if err := m.PingServers(); err != nil {
					m.logger.Error("Failed to ping servers after update",
						logger.ErrorField(err))
				}
			}()

			return nil
		}
	}

	// If no providers or all providers failed
	if !atLeastOneProviderTried {
		return fmt.Errorf("no server list providers available")
	}

	// If all providers failed but we have existing server list, keep existing list
	//m.mutex.RLock()
	hasExistingServers := len(m.servers) > 0
	//m.mutex.RUnlock()

	if hasExistingServers {
		m.logger.Warn("Failed to update server list from any provider, keeping existing list")
		return nil
	}

	return fmt.Errorf("failed to update server list from any provider")
}

/*****************************************************************************
 * NAME: GetCurrentConnectionServer
 *
 * DESCRIPTION:
 *     Gets the currently connected server
 *
 * RETURNS:
 *     *Server - Copy of currently connected server, nil if none
 *****************************************************************************/
func (m *Manager) GetCurrentConnectionServer() *Server {
	// If no default server set, return nil
	if m.defaultServerID == "" {
		return nil
	}

	// Find currently connected server
	for _, server := range m.servers {
		if server.ID == m.defaultServerID {
			serverCopy := server
			return &serverCopy
		}
	}

	return nil
}

/*****************************************************************************
 * NAME: SetServerListURL
 *
 * DESCRIPTION:
 *     Sets server list URL dynamically from frontend
 *     Updates the server list provider and fetches new server list
 *
 * PARAMETERS:
 *     url - Server list URL from frontend
 *
 * RETURNS:
 *     error - Error if setting URL fails
 *****************************************************************************/
func (m *Manager) SetServerListURL(url string) error {
	m.logger.Info("Setting server list URL from frontend", logger.String("url", url))

	// Update configuration, use frontend URL as priority, no longer use URL from config file
	m.config.ServerListURL = url

	// Remove all existing providers (including HTTP and file providers)
	// Use frontend updates as priority, no longer use url or file read from config file
	m.providers = []Provider{}

	// Add new HTTP provider (only use URL passed from frontend)
	if url != "" {
		m.logger.Info("Adding HTTP provider with frontend URL", logger.String("url", url))
		m.AddProvider(NewHTTPProvider(url, 10*time.Second, m.config.TLSSkipVerify, m.logger))
	} else {
		m.logger.Warn("Frontend provided empty URL, no providers will be added")
		return fmt.Errorf("empty server list URL provided from frontend")
	}

	// Synchronously update server list and wait for ping completion
	// Use longer timeout to wait for ping completion, ensure accurate latency information
	pingTimeout := 30 * time.Second // Set 30 second timeout
	err := m.UpdateServerListAndWaitPing(pingTimeout)
	if err != nil {
		m.logger.Error("Failed to update server list after setting URL", logger.ErrorField(err))
		return err
	}

	// Synchronously update ping probe targets
	// After server list update, ping probe targets will be automatically updated through PingServers method
	m.logger.Info("Server list and ping targets updated successfully with frontend URL")

	return nil
}

/*****************************************************************************
 * NAME: SelectBestAutoServer
 *
 * DESCRIPTION:
 *     Selects the best automatic server based on latency
 *
 * RETURNS:
 *     *Server - Copy of best automatic server
 *     error   - Error if no automatic servers available
 *****************************************************************************/
func (m *Manager) SelectBestAutoServer() (*Server, error) {
	m.logger.Info("Selecting best auto server")

	// Get all servers
	servers := m.GetServers()

	// Filter automatic servers - no longer filter by status, consider all automatic servers
	var autoServers []Server
	for _, server := range servers {
		if server.IsAuto {
			autoServers = append(autoServers, server)
		}
	}

	// Check if there are auto servers
	if len(autoServers) == 0 {
		m.logger.Info("No auto servers found")
		return nil, fmt.Errorf("no auto servers found")
	}

	// Sort by latency - prioritize online servers first, then sort by latency
	sort.Slice(autoServers, func(i, j int) bool {
		// Prioritize online servers
		if autoServers[i].Status == "online" && autoServers[j].Status != "online" {
			return true
		}
		if autoServers[i].Status != "online" && autoServers[j].Status == "online" {
			return false
		}

		// If status is the same, sort by latency
		// Treat servers with 0 latency as high latency
		pingI := autoServers[i].Ping
		if pingI == 0 {
			pingI = 9999
		}
		pingJ := autoServers[j].Ping
		if pingJ == 0 {
			pingJ = 9999
		}
		return pingI < pingJ
	})

	// Select the best server
	bestServer := autoServers[0]

	// Record the selected best server
	// m.mutex.Lock()
	m.defaultServerID = bestServer.ID
	// m.mutex.Unlock()

	m.logger.Info("Selected best auto server",
		logger.String("id", bestServer.ID),
		logger.String("name", bestServer.Name),
		logger.String("status", bestServer.Status),
		logger.Int("ping", bestServer.Ping))

	return &bestServer, nil
}

/*****************************************************************************
 * NAME: GetCachedServerIP
 *
 * DESCRIPTION:
 *     Gets cached IP address for a hostname.
 *     Returns cached IP if available, empty string otherwise.
 *
 * PARAMETERS:
 *     hostname - Server hostname to lookup
 *
 * RETURNS:
 *     string - Cached IP address or empty string if not cached
 *****************************************************************************/
func (m *Manager) GetCachedServerIP(hostname string) string {
	m.ipCacheMutex.RLock()
	defer m.ipCacheMutex.RUnlock()

	if ip, exists := m.serverIPCache[hostname]; exists {
		return ip
	}
	return ""
}

/*****************************************************************************
 * NAME: ResolveServerIP
 *
 * DESCRIPTION:
 *     Resolves server hostname to IP address with caching.
 *     Uses cache first, performs DNS resolution if needed.
 *     Returns empty string if resolution fails, allowing caller to handle fallback.
 *
 * PARAMETERS:
 *     hostname - Server hostname to resolve
 *
 * RETURNS:
 *     string - Resolved IP address or empty string if failed (caller should fallback to hostname)
 *****************************************************************************/
func (m *Manager) ResolveServerIP(hostname string) string {
	// Check cache first
	if cachedIP := m.GetCachedServerIP(hostname); cachedIP != "" {
		return cachedIP
	}

	// Resolve and cache
	resolvedIP, err := m.resolveHostnameToIP(hostname)
	if err != nil {
		m.logger.Warn("Failed to resolve server IP, caller should fallback to hostname",
			logger.String("hostname", hostname),
			logger.String("error", err.Error()))
		return "" // Return empty string to indicate failure, caller handles fallback
	}

	if resolvedIP != "" {
		m.ipCacheMutex.Lock()
		m.serverIPCache[hostname] = resolvedIP
		m.ipCacheMutex.Unlock()

		m.logger.Debug("Cached server IP",
			logger.String("hostname", hostname),
			logger.String("ip", resolvedIP))
	}

	return resolvedIP
}

/*****************************************************************************
 * NAME: GetAllCachedServerIPs
 *
 * DESCRIPTION:
 *     Gets all cached server IP addresses for route exclusion.
 *     Used by ConnectionManager to exclude server IPs from VPN routing.
 *
 * RETURNS:
 *     []string - List of all cached server IP addresses
 *****************************************************************************/
func (m *Manager) GetAllCachedServerIPs() []string {
	m.ipCacheMutex.RLock()
	defer m.ipCacheMutex.RUnlock()

	ips := make([]string, 0, len(m.serverIPCache))
	for _, ip := range m.serverIPCache {
		ips = append(ips, ip)
	}
	return ips
}

/*****************************************************************************
 * NAME: ClearIPCache
 *
 * DESCRIPTION:
 *     Clears all cached IP addresses.
 *     Useful for testing or when network configuration changes.
 *****************************************************************************/
func (m *Manager) ClearIPCache() {
	m.ipCacheMutex.Lock()
	defer m.ipCacheMutex.Unlock()

	m.serverIPCache = make(map[string]string)
	m.logger.Info("IP cache cleared")
}

/*****************************************************************************
 * NAME: resolveHostnameToIP
 *
 * DESCRIPTION:
 *     Resolves hostname to IP address using system DNS.
 *     This is a private helper method for DNS resolution.
 *
 * PARAMETERS:
 *     hostname - Hostname to resolve
 *
 * RETURNS:
 *     string - Resolved IP address or empty string if failed
 *     error  - Error if resolution fails
 *****************************************************************************/
func (m *Manager) resolveHostnameToIP(hostname string) (string, error) {
	// Check if hostname is already an IP address
	if ip := net.ParseIP(hostname); ip != nil {
		return hostname, nil
	}

	// Resolve hostname using system DNS
	ips, err := net.LookupIP(hostname)
	if err != nil {
		return "", fmt.Errorf("DNS resolution failed: %w", err)
	}

	// Return the first IPv4 address found
	for _, ip := range ips {
		if ipv4 := ip.To4(); ipv4 != nil {
			return ipv4.String(), nil
		}
	}

	// If no IPv4 found, return the first IP (could be IPv6)
	if len(ips) > 0 {
		return ips[0].String(), nil
	}

	return "", fmt.Errorf("no IP addresses found for hostname: %s", hostname)
}
